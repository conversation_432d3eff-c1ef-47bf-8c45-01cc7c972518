using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Pool;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Data class representing a prefab with its spawn weight.
    /// </summary>
    [System.Serializable]
    public class SpawnablePrefab
    {
        /// <summary>
        /// The prefab GameObject to spawn.
        /// </summary>
        public GameObject prefab;

        /// <summary>
        /// The weight for random selection.
        /// </summary>
        [Range(0.01f, 100f)]
        public float spawnWeight = 1f;
    }

    /// <summary>
    /// Controls the spawning of objects using object pooling, proximity checks, and visibility awareness.
    /// </summary>
    public class ObjectSpawner : MonoBehaviour
    {
        #region Fields

        [SerializeField] private Transform targetTransform;
        [SerializeField] private ObjectSpawnerData data;

        #endregion

        #region Properties

        /// <summary>
        /// The target transform around which objects are spawned.
        /// </summary>
        public Transform TargetTransform
        {
            get => targetTransform;
            set => targetTransform = value;
        }

        #endregion

        #region Private Fields

        private readonly Dictionary<GameObject, ObjectPool<GameObject>> objectPools = new();
        private readonly List<SpawnedObjectData> activeObjects = new();
        private float spawnTimer;
        private float totalSpawnWeight;

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            InitializePools();
            CalculateTotalWeight();
        }

        private void Update()
        {
            if (targetTransform == null) return;
            spawnTimer -= Time.deltaTime;
            if (spawnTimer <= 0 && activeObjects.Count < data.maxConcurrentObjects)
            {
                TrySpawn();
                spawnTimer = data.spawnInterval;
            }
            CheckDespawn();
        }

        #endregion

        #region Private Methods

        private void InitializePools()
        {
            foreach (var prefabData in data.prefabs)
            {
                var pool = new ObjectPool<GameObject>(
                    () => CreatePooledItem(prefabData.prefab),
                    OnTakeFromPool,
                    OnReturnToPool,
                    OnDestroyPoolObject,
                    false,
                    data.defaultPoolSize,
                    data.maxPoolSize
                );
                objectPools.Add(prefabData.prefab, pool);
            }
        }

        private GameObject CreatePooledItem(GameObject prefab)
        {
            var obj = Instantiate(prefab);
            obj.SetActive(false);
            return obj;
        }

        private void OnTakeFromPool(GameObject obj) => obj.SetActive(true);

        private void OnReturnToPool(GameObject obj) => obj.SetActive(false);

        private void OnDestroyPoolObject(GameObject obj) => Destroy(obj);

        private void CalculateTotalWeight()
        {
            totalSpawnWeight = 0f;
            foreach (var prefabData in data.prefabs)
            {
                totalSpawnWeight += prefabData.spawnWeight;
            }
        }

        private void TrySpawn()
        {
            var selectedPrefab = SelectWeightedPrefab();
            if (selectedPrefab == null) return;

            for (var attempt = 0; attempt < data.maxSpawnAttempts; attempt++)
            {
                var spawnPosition = GetRandomSpawnPosition();
                if (IsValidSpawnPosition(spawnPosition, selectedPrefab))
                {
                    SpawnObject(selectedPrefab, spawnPosition);
                    return;
                }

                if (data.debugMode)
                {
                    Debug.LogWarning($"Spawn attempt {attempt + 1} failed, trying a new position.");
                }
            }

            if (data.debugMode)
            {
                Debug.LogWarning($"Failed to find valid spawn position after {data.maxSpawnAttempts} attempts.");
            }
        }

        private GameObject SelectWeightedPrefab()
        {
            var randomValue = Random.Range(0, totalSpawnWeight);
            var weightSum = 0f;

            foreach (var prefabData in data.prefabs)
            {
                weightSum += prefabData.spawnWeight;
                if (randomValue <= weightSum)
                {
                    return prefabData.prefab;
                }
            }

            return data.prefabs.Count > 0 ? data.prefabs[0].prefab : null;
        }

        private Vector3 GetRandomSpawnPosition()
        {
            var randomAngle = Random.Range(0f, 360f);
            var randomDistance = Random.Range(data.spawnRange * 0.6f, data.spawnRange);
            var offset = new Vector3(
                Mathf.Sin(randomAngle * Mathf.Deg2Rad) * randomDistance,
                0,
                Mathf.Cos(randomAngle * Mathf.Deg2Rad) * randomDistance
            );
            var position = targetTransform.position + offset;
            position.y = targetTransform.position.y + Random.Range(data.ySpawnRange.x, data.ySpawnRange.y);
            return position;
        }

        private void SpawnObject(GameObject prefab, Vector3 position)
        {
            if (objectPools.TryGetValue(prefab, out var pool))
            {
                var obj = pool.Get();
                obj.transform.position = position;
                float randomRotation = Random.Range(0f, 360f);
                obj.transform.rotation = Quaternion.Euler(0, randomRotation, 0);
                activeObjects.Add(new SpawnedObjectData
                {
                    gameObject = obj,
                    prefab = prefab,
                    spawnTime = Time.time
                });
            }
        }

        private void CheckDespawn()
        {
            for (var i = activeObjects.Count - 1; i >= 0; i--)
            {
                var objectData = activeObjects[i];
                float distanceToTarget = Vector3.Distance(
                    objectData.gameObject.transform.position,
                    targetTransform.position
                );

                if (distanceToTarget > data.despawnRange && IsOutsideViewCone(objectData.gameObject.transform.position))
                {
                    if (objectPools.TryGetValue(objectData.prefab, out var pool))
                    {
                        pool.Release(objectData.gameObject);
                    }
                    activeObjects.RemoveAt(i);
                }
            }
        }

        private bool IsOutsideViewCone(Vector3 position)
        {
            var directionToPosition = (position - targetTransform.position).normalized;
            var angle = Vector3.Angle(targetTransform.forward, directionToPosition);
            return angle > data.referenceAngle / 2f;
        }

        #endregion

        #region Debug Support

        private class DebugPositionInfo
        {
            public Vector3 Position;
            public bool IsValid;
            public string RejectReason;
            public Vector3[] RaycastDirections;
            public Vector3[] RaycastHitPoints;
            public Bounds BoxBounds;
            public float Time;
        }

        private readonly List<DebugPositionInfo> debugPositions = new();
        private const float DebugInfoDisplayTime = 5f;

        private bool IsValidSpawnPosition(Vector3 position, GameObject prefab)
        {
            DebugPositionInfo debugInfo = null;
            if (data.debugMode)
            {
                debugInfo = new DebugPositionInfo
                {
                    Position = position,
                    IsValid = true,
                    Time = Time.time,
                    RaycastDirections = new Vector3[0],
                    RaycastHitPoints = new Vector3[0]
                };
            }

            if (!IsOutsideViewCone(position))
            {
                if (data.debugMode)
                {
                    debugInfo.IsValid = false;
                    debugInfo.RejectReason = "Inside view cone";
                    debugPositions.Add(debugInfo);
                    Debug.LogWarning("Position rejected: Inside view cone");
                }
                return false;
            }

            var hitColliders = Physics.OverlapSphere(position, data.collisionCheckRadius, data.obstructionLayers);
            if (hitColliders.Length > 0)
            {
                if (data.debugMode)
                {
                    debugInfo.IsValid = false;
                    debugInfo.RejectReason = $"Colliding with {hitColliders[0].name}";
                    debugPositions.Add(debugInfo);
                    Debug.LogWarning($"Position rejected: Colliding with {hitColliders[0].name}");
                }
                return false;
            }

            var prefabCollider = prefab.GetComponent<Collider>();
            if (prefabCollider != null)
            {
                var bounds = prefabCollider.bounds;
                var extents = bounds.extents;
                var center = position;
                var adjustedBounds = new Bounds(center, extents * 2);
                var overlapColliders = Physics.OverlapBox(
                    center,
                    extents,
                    Quaternion.identity,
                    data.obstructionLayers
                );

                if (overlapColliders.Length > 0)
                {
                    if (data.debugMode)
                    {
                        debugInfo.IsValid = false;
                        debugInfo.RejectReason = $"Prefab would collide with {overlapColliders[0].name}";
                        debugInfo.BoxBounds = adjustedBounds;
                        debugPositions.Add(debugInfo);
                        Debug.LogWarning($"Position rejected: Prefab would collide with {overlapColliders[0].name}");
                    }
                    return false;
                }

                if (data.debugMode)
                {
                    debugInfo.BoxBounds = adjustedBounds;
                }
            }
            Vector3[] directions = {
                Vector3.forward, Vector3.back, Vector3.left, Vector3.right, Vector3.up, Vector3.down,
                Vector3.forward + Vector3.right, Vector3.forward + Vector3.left,
                Vector3.back + Vector3.right, Vector3.back + Vector3.left,
                Vector3.up + Vector3.right, Vector3.up + Vector3.left,
                Vector3.down + Vector3.right, Vector3.down + Vector3.left
            };

            if (data.debugMode)
            {
                debugInfo.RaycastDirections = directions;
                debugInfo.RaycastHitPoints = new Vector3[directions.Length];
            }

            for (var i = 0; i < directions.Length; i++)
            {
                var dir = directions[i].normalized;
                if (Physics.SphereCast(position, 1f, dir, out RaycastHit hit, data.collisionCheckLength, data.obstructionLayers))
                {
                    if (data.debugMode)
                    {
                        debugInfo.IsValid = false;
                        debugInfo.RejectReason = "Edge raycast hit obstruction";
                        if (i < debugInfo.RaycastHitPoints.Length)
                        {
                            debugInfo.RaycastHitPoints[i] = hit.point;
                        }
                        debugPositions.Add(debugInfo);
                        Debug.LogWarning("Position rejected: Edge raycast hit obstruction");
                    }
                    return false;
                }
            }

            if (data.debugMode)
            {
                debugPositions.Add(debugInfo);
            }

            return true;
        }

        private void OnDrawGizmos()
        {
            if (data == null || !data.debugMode) return;
            debugPositions.RemoveAll(p => Time.time - p.Time > DebugInfoDisplayTime);
            if (targetTransform != null)
            {
                Gizmos.color = new Color(1, 1, 0, 0.3f);
                DrawViewCone(targetTransform.position, targetTransform.forward, data.referenceAngle, data.spawnRange);
                Gizmos.color = new Color(0, 1, 0, 0.2f);
                Gizmos.DrawWireSphere(targetTransform.position, data.spawnRange);
                Gizmos.color = new Color(1, 0, 0, 0.2f);
                Gizmos.DrawWireSphere(targetTransform.position, data.despawnRange);
            }
            foreach (var debugInfo in debugPositions)
            {
                if (debugInfo.IsValid)
                {
                    Gizmos.color = new Color(0, 1, 0, 0.5f);
                    Gizmos.DrawSphere(debugInfo.Position, data.collisionCheckRadius * 0.5f);
                    Gizmos.DrawLine(debugInfo.Position, debugInfo.Position + Vector3.up * 1f);
                    Gizmos.DrawLine(debugInfo.Position + Vector3.up * 1f,
                        debugInfo.Position + Vector3.up * 0.5f + Vector3.right * 0.5f);
                    Gizmos.DrawLine(debugInfo.Position + Vector3.up * 1f,
                        debugInfo.Position + Vector3.up * 0.5f + Vector3.left * 0.5f);
                }
                else
                {
                    Gizmos.color = new Color(1, 0, 0, 0.5f);
                    Gizmos.DrawSphere(debugInfo.Position, data.collisionCheckRadius * 0.5f);
                    Vector3 offset = Vector3.one * 0.5f;
                    Gizmos.DrawLine(debugInfo.Position + offset, debugInfo.Position - offset);
                    Gizmos.DrawLine(debugInfo.Position + new Vector3(offset.x, offset.y, -offset.z),
                        debugInfo.Position + new Vector3(-offset.x, -offset.y, offset.z));
#if UNITY_EDITOR
                    if (!string.IsNullOrEmpty(debugInfo.RejectReason))
                    {
                        Handles.Label(debugInfo.Position + Vector3.up * 1.2f, debugInfo.RejectReason);
                    }
#endif
                }
                if (debugInfo.BoxBounds.size != Vector3.zero)
                {
                    Gizmos.color = debugInfo.IsValid ? new Color(0, 1, 0, 0.3f) : new Color(1, 0, 0, 0.3f);
                    Gizmos.DrawWireCube(debugInfo.BoxBounds.center, debugInfo.BoxBounds.size);
                }
                if (debugInfo.RaycastDirections != null)
                {
                    for (var i = 0; i < debugInfo.RaycastDirections.Length; i++)
                    {
                        var dir = debugInfo.RaycastDirections[i].normalized;
                        Vector3 end;
                        if (i < debugInfo.RaycastHitPoints.Length && debugInfo.RaycastHitPoints[i] != Vector3.zero)
                        {
                            Gizmos.color = Color.red;
                            end = debugInfo.RaycastHitPoints[i];
                        }
                        else
                        {
                            Gizmos.color = Color.green;
                            end = debugInfo.Position + dir * data.collisionCheckLength;
                        }
                        Gizmos.DrawLine(debugInfo.Position, end);
                    }
                }
            }
        }

        private void DrawViewCone(Vector3 position, Vector3 direction, float angle, float radius)
        {
            const int segments = 20;
            float halfAngle = angle / 2f;
            Vector3 leftDir = Quaternion.Euler(0, -halfAngle, 0) * direction;
            Vector3 rightDir = Quaternion.Euler(0, halfAngle, 0) * direction;
            Vector3 prevLeft = position + leftDir * radius;
            Vector3 prevRight = position + rightDir * radius;
            Gizmos.DrawLine(position, prevLeft);
            Gizmos.DrawLine(position, prevRight);
            for (var i = 1; i <= segments; i++)
            {
                float t = i / (float)segments;
                float currentAngle = -halfAngle + angle * t;
                Vector3 currentDir = Quaternion.Euler(0, currentAngle, 0) * direction;
                Vector3 currentPoint = position + currentDir * radius;
                Gizmos.DrawLine(prevLeft, currentPoint);
                prevLeft = currentPoint;
            }
        }


        #endregion

        #region Public Methods

        /// <summary>
        /// Adds a new prefab to the spawn list.
        /// </summary>
        /// <param name="prefab">The prefab to add.</param>
        /// <param name="weight">The spawn weight.</param>
        public void AddPrefab(GameObject prefab, float weight = 1f)
        {
            if (prefab == null) return;

            foreach (var existingPrefab in data.prefabs)
            {
                if (existingPrefab.prefab == prefab)
                {
                    existingPrefab.spawnWeight = weight;
                    CalculateTotalWeight();
                    return;
                }
            }

            var newPrefab = new SpawnablePrefab
            {
                prefab = prefab,
                spawnWeight = weight
            };
            data.prefabs.Add(newPrefab);

            var pool = new ObjectPool<GameObject>(
                () => CreatePooledItem(prefab),
                OnTakeFromPool,
                OnReturnToPool,
                OnDestroyPoolObject,
                false,
                10,
                100
            );
            objectPools.Add(prefab, pool);
            CalculateTotalWeight();
        }

        /// <summary>
        /// Despawns all active objects.
        /// </summary>
        public void DespawnAll()
        {
            for (var i = activeObjects.Count - 1; i >= 0; i--)
            {
                var objectData = activeObjects[i];
                if (objectPools.TryGetValue(objectData.prefab, out var pool))
                {
                    pool.Release(objectData.gameObject);
                }
            }
            activeObjects.Clear();
        }

        #endregion
    }

    /// <summary>
    /// Holds data for a spawned object.
    /// </summary>
    public class SpawnedObjectData
    {
        /// <summary>
        /// The spawned GameObject instance.
        /// </summary>
        public GameObject gameObject;

        /// <summary>
        /// The prefab from which this object was spawned.
        /// </summary>
        public GameObject prefab;

        /// <summary>
        /// The time at which the object was spawned.
        /// </summary>
        public float spawnTime;
    }
}